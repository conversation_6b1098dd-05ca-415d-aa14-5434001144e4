# Automatic Fee Application System

## Overview

This system automatically applies fee rules in two scenarios:

### 1. New Checkout ID Received
When a checkout ID is submitted to the `/api/receive-checkout-id` endpoint, the system will:
1. Store the checkout ID in Firebase
2. Fetch all active fee rules for the store
3. Apply each fee rule to the checkout using the BigCommerce Checkout Fees API
4. Return detailed results about the fee application process

### 2. New Fee Rule Created
When a new fee rule is created through the `/api/fee-rules` endpoint, the system will:
1. Store the fee rule in Firebase
2. If the fee rule is active, fetch the most recent checkout ID
3. Apply all active fee rules (including the newly created one) to that checkout
4. Return detailed results about both fee rule creation and fee application

### 3. Fee Rule Updated
When a fee rule is updated through the `/api/fee-rules` endpoint (PUT), the system will:
1. Update the fee rule in Firebase
2. If the fee rule is active, find the most recent checkout with an applied fee for this rule
3. Update the corresponding BigCommerce fee with the new fee data
4. Return detailed results about both fee rule update and fee synchronization

### 4. Fee Rule Deleted
When a fee rule is deleted through the `/api/fee-rules` endpoint (DELETE), the system will:
1. Find the most recent checkout with an applied fee for this rule
2. Remove the corresponding BigCommerce fee from the checkout
3. Delete the fee rule from Firebase
4. Return detailed results about both fee rule deletion and fee synchronization

## Architecture

### Core Components

1. **BigCommerce API Service** (`lib/bigcommerce-api.ts`)
   - Handles authentication with BigCommerce API using ACCESS_TOKEN
   - Provides functions to fetch checkout details and apply fees
   - Includes error handling for API responses

2. **Fee Application Service** (`lib/fee-application.ts`)
   - Orchestrates the fee application process
   - Fetches active fee rules from Firebase
   - Calculates percentage-based fees using checkout grand total
   - Converts fee rules to BigCommerce API format

3. **Modified Endpoint** (`pages/api/receive-checkout-id.ts`)
   - Enhanced to automatically apply fees after storing checkout ID
   - Provides detailed response including fee application results
   - Gracefully handles fee application errors without failing the main request

### Data Flow

#### Checkout ID Received Flow
```
Checkout ID Received
        ↓
Store in Firebase
        ↓
Fetch Active Fee Rules
        ↓
Get Checkout Details (if percentage fees exist)
        ↓
Calculate Fee Costs
        ↓
Apply Fees via BigCommerce API
        ↓
Update Fee Rule Tracking with BigCommerce Fee IDs
        ↓
Return Results with Tracking Information
```

#### Fee Rule Created Flow
```
Fee Rule Created
        ↓
Store in Firebase
        ↓
If Active Fee Rule
        ↓
Fetch Most Recent Checkout ID
        ↓
Apply All Active Fee Rules to Checkout
        ↓
Update Fee Rule Tracking with BigCommerce Fee IDs
        ↓
Return Fee Rule Creation + Application + Tracking Results
```

#### Fee Rule Updated Flow
```
Fee Rule Updated
        ↓
Update in Firebase
        ↓
If Active Fee Rule
        ↓
Find Recent Checkout with Applied Fee
        ↓
Validate Checkout Still Accessible
        ↓
Update BigCommerce Fee with New Data
        ↓
Update Fee Rule Tracking
        ↓
Return Fee Rule Update + Synchronization Results
```

#### Fee Rule Deleted Flow
```
Fee Rule Deleted
        ↓
Find Recent Checkout with Applied Fee
        ↓
Validate Checkout Still Accessible
        ↓
Delete BigCommerce Fee from Checkout (Bulk Deletion API)
        ↓
Verify Fee Removal from Checkout
        ↓
Delete Fee Rule from Firebase
        ↓
Return Fee Rule Deletion + Synchronization Results
```

## Fee Rule Matching Logic

Fee rules are applied to checkouts based on comprehensive matching criteria. A fee rule applies when **ALL** of the following conditions are met:

### 1. Customer Group Validation
- **If `selectedCustomerGroups` is specified**: The customer's group ID must match exactly
- **If `selectedCustomerGroups` is empty**: No customer group filtering (applies to all customers)
- **If customer data is unavailable**: Fee rules requiring specific customer groups are skipped

### 2. Channel ID Validation
- **If `selectedChannels` is specified**: The checkout's channel ID must match exactly
- **If `selectedChannels` is empty**: No channel filtering (applies to all channels)
- **If channel data is unavailable**: Fee rules requiring specific channels are skipped

### 3. Product Criteria Matching (OR Logic)
At least **ONE** of the following product criteria must match:
- **Direct Product Selection**: Product is included in `selectedProducts` array
- **Brand Matching**: Product's brand matches any brand in `selectedBrands`
- **Category Matching**: Product's category matches any category in `selectedCategories`
- **If all product filters are empty**: No product filtering (applies to all products)

### 4. Active Status
- Only fee rules with `active: true` are considered for application

### Validation Examples

**Scenario**: Cart contains product p1 with brand b2, category c1, customer group ID 1, channel ID 1

**✅ Should Apply:**
- Fee rule: `products=[p1], customer_group=1, channel=1` → Apply (product match)
- Fee rule: `categories=[c1], customer_group=1, channel=1` → Apply (category match)
- Fee rule: `brands=[b2], customer_group=1, channel=1` → Apply (brand match)
- Fee rule: `products=[p1], brands=[b3], customer_group=1, channel=1` → Apply (product match, brand ignored)

**❌ Should NOT Apply:**
- Fee rule: `categories=[c2,c3], brands=[b3], products=[p4], customer_group=1, channel=1` → Don't apply (no product criteria match)
- Fee rule: `products=[p1], customer_group=2, channel=1` → Don't apply (customer group mismatch)
- Fee rule: `products=[p1], customer_group=1, channel=2` → Don't apply (channel ID mismatch)

## Fee Types

### Fixed Amount Fees
- Applied directly as specified in the fee rule
- Example: $5.00 processing fee

### Percentage Fees
- Calculated based on checkout grand total or filtered product totals
- Example: 3% of checkout total
- Uses sale price when available (> 0), falls back to regular price
- Requires fetching checkout details first

## API Usage

### BigCommerce Checkout Fees API Format
The system uses the correct BigCommerce API format for applying fees:

**Request:**
```json
POST /stores/{store_hash}/v3/checkouts/{checkoutId}/fees
{
  "fees": [
    {
      "type": "custom_fee",
      "name": "processing_fee",
      "display_name": "Processing Fee",
      "cost": 5.00,
      "source": "app",
      "tax_class_id": 1
    }
  ]
}
```

**Response:**
The API returns the complete checkout object with applied fees nested in the `fees` array:
```json
{
  "data": {
    "id": "checkout_123",
    "grand_total": 105.00,
    "fees": [
      {
        "id": "fee_456",
        "type": "custom_fee",
        "name": "processing_fee",
        "display_name": "Processing Fee",
        "cost_ex_tax": 4.55,
        "cost_inc_tax": 5.00,
        "source": "app",
        "tax_class_id": 1
      }
    ],
    ...
  }
}
```

**Note:** The BigCommerce API returns `cost_ex_tax` and `cost_inc_tax` properties instead of a simple `cost` property. The system handles this automatically and provides backward compatibility.

### API Usage Examples

#### Checkout ID Submission
```json
POST /api/receive-checkout-id
{
  "checkoutId": "checkout_123",
  "storeHash": "store_abc123"
}
```

#### Fee Rule Creation
```json
POST /api/fee-rules
{
  "name": "processing_fee",
  "display_name": "Processing Fee",
  "type": "fixed",
  "cost": 5.00,
  "source": "APP",
  "active": true
}
```

### Response Formats

#### Checkout ID Response
```json
{
  "success": true,
  "message": "Checkout ID stored successfully",
  "data": {
    "checkoutId": "checkout_123",
    "storeHash": "store_abc123",
    "feeApplication": {
      "success": true,
      "appliedFeesCount": 2,
      "errorsCount": 0,
      "appliedFees": [
        {
          "id": "fee_456",
          "name": "processing_fee",
          "cost": 5.00
        }
      ],
      "errors": [],
      "trackingUpdates": [
        {
          "feeRuleId": "rule_123",
          "feeRuleName": "processing_fee",
          "bigCommerceFeeId": "fee_456",
          "success": true
        }
      ]
    }
  }
}
```

#### Fee Rule Creation Response
```json
{
  "success": true,
  "message": "Fee rule created successfully",
  "data": {
    "id": "rule_789",
    "name": "processing_fee",
    "display_name": "Processing Fee",
    "type": "fixed",
    "cost": 5.00,
    "source": "APP",
    "active": true
  },
  "feeApplication": {
    "attempted": true,
    "success": true,
    "appliedFeesCount": 3,
    "errorsCount": 0,
    "checkoutId": "checkout_123",
    "details": {
      "appliedFees": [
        {
          "id": "fee_456",
          "name": "processing_fee",
          "cost": 5.00
        }
      ],
      "errors": [],
      "trackingUpdates": [
        {
          "feeRuleId": "rule_789",
          "feeRuleName": "processing_fee",
          "bigCommerceFeeId": "fee_456",
          "success": true
        }
      ]
    }
  }
}
```

#### Fee Rule Update Response
```json
{
  "success": true,
  "message": "Fee rule updated successfully",
  "data": {
    "id": "rule_789",
    "name": "processing_fee",
    "display_name": "Updated Processing Fee",
    "type": "fixed",
    "cost": 7.50,
    "source": "APP",
    "active": true
  },
  "synchronization": {
    "attempted": true,
    "success": true,
    "action": "update",
    "checkoutId": "checkout_123",
    "bigCommerceFeeId": "fee_456",
    "details": {
      "feeRuleId": "rule_789",
      "feeRuleName": "processing_fee",
      "checkoutFound": true,
      "feeHistoryFound": true,
      "bigCommerceOperationSuccess": true
    }
  }
}
```

#### Fee Rule Deletion Response
```json
{
  "success": true,
  "message": "Fee rule deleted successfully",
  "data": {
    "feeRuleId": "rule_789"
  },
  "synchronization": {
    "attempted": true,
    "success": true,
    "action": "delete",
    "checkoutId": "checkout_123",
    "bigCommerceFeeId": "fee_456",
    "details": {
      "feeRuleId": "rule_789",
      "feeRuleName": "processing_fee",
      "checkoutFound": true,
      "feeHistoryFound": true,
      "bigCommerceOperationSuccess": true
    }
  }
}
```

## Configuration

### Environment Variables
- `ACCESS_TOKEN`: BigCommerce API access token (required)
- `DB_TYPE`: Set to "firebase" for Firebase database

### Fee Rule Structure in Firebase
```
stores/{storeHash}/feeRules/{feeRuleId}
{
  "name": "processing_fee",
  "display_name": "Processing Fee",
  "type": "fixed", // or "percentage"
  "cost": 5.00,
  "active": true,
  "source": "APP", // Optional - defaults to "APP" if not specified
  "tax_class_id": 1, // Optional
  "created_at": 1234567890,

  // Fee tracking fields (automatically managed)
  "appliedFeeId": "fee_abc123", // Most recent BigCommerce fee ID
  "lastAppliedAt": 1234567890, // Timestamp of last application
  "appliedFeeHistory": [ // History of all applications
    {
      "feeId": "fee_abc123",
      "checkoutId": "checkout_456",
      "appliedAt": 1234567890,
      "cost": 5.00
    }
  ]
}
```

**Note:** The `source` field is required by the BigCommerce API but optional in Firebase. If not specified, the system automatically uses "APP" as the default value.

**Fee Tracking:** The system automatically tracks which BigCommerce fees correspond to which fee rules, maintaining both the most recent application and a complete history.

**Percentage Fee Calculation:** Percentage fees are calculated using `cart_amount_inc_tax` (cart subtotal including tax) rather than `grand_total` to prevent compounding calculations on fees, shipping, and handling costs. When product filters are applied, the calculation uses individual product prices, preferring `sale_price` when it's valid (> 0) and falling back to regular `price` if `sale_price` is 0, null, or undefined.

## Error Handling

- Fee application errors don't fail the main checkout ID storage
- Detailed error information is returned in the response
- All errors are logged for debugging
- Network timeouts and API errors are handled gracefully
- Duplicate fee detection prevents applying the same fee multiple times
- Smart fee matching handles BigCommerce API response structure differences

## Logging

The system provides comprehensive logging:
- Fee rule fetching and filtering
- Checkout details retrieval
- Individual fee applications
- Error details and stack traces

## Testing

To test the fee application system:

1. Create active fee rules in Firebase
2. Send a POST request to `/api/receive-checkout-id`
3. Check the response for fee application results
4. Verify fees appear in BigCommerce checkout

## Troubleshooting

### Common Issues

1. **No fees applied**: Check if fee rules are marked as `active: true`
2. **Percentage calculation errors**: Ensure checkout exists and has valid cart_amount_inc_tax (percentage fees calculated on cart value, not grand total). For filtered fees, ensure products have valid price values (sale_price=0 will automatically fall back to regular price)
3. **API authentication errors**: Verify ACCESS_TOKEN is correct and has proper permissions
4. **Firebase connection issues**: Check Firebase configuration and credentials
5. **TypeError: Cannot read properties of undefined (reading 'id')**: This was caused by incorrect response handling. The API returns the full checkout object with fees nested inside, not just the applied fee. This has been fixed in the current implementation.
6. **422 "The fees field is required"**: Ensure fee data is wrapped in a `fees` array when making API requests
7. **Cost property showing as undefined**: The BigCommerce API returns `cost_ex_tax` and `cost_inc_tax` instead of `cost`. The system now handles this automatically and provides backward compatibility.
8. **Duplicate fees being applied**: The system now checks for existing fees with the same name before applying new ones to prevent duplicates.
9. **"Could not find exact match for applied fee"**: Improved fee matching algorithm that handles BigCommerce API response structure and finds fees by name and type rather than exact cost matching.
10. **422 "The source field is required"**: The BigCommerce API requires a `source` field for all custom fees. The system now automatically provides "APP" as the default value if not specified in the fee rule.
11. **"Unknown error occurred" instead of specific API errors**: Enhanced error handling now captures and reports detailed BigCommerce API validation errors with specific field information.
12. **404 "Not Found" on fee update/delete**: Fixed by using correct BigCommerce API endpoints - fee ID goes in request body for updates, not URL path. Fee deletion uses bulk deletion API with fallback to workaround method.
13. **Fee synchronization failures**: 404 errors are now treated as successful operations since they indicate the fee/checkout no longer exists (normal for completed checkouts).
14. **422 "The ids field is required" on fee deletion**: Fixed by enabling request body for DELETE requests in the API client - the bulk deletion API requires the ids array in the request body.

### BigCommerce API Implementation Details

**Fee Update Endpoint:**
- **Correct**: `PUT /stores/{store_hash}/v3/checkouts/{checkoutId}/fees`
- **Request Body**: Fee ID must be included in the request body, not the URL path
- **Format**: `{"fees":[{"id":"existing-fee-id","type":"custom_fee","name":"fee_name","display_name":"Display Name","cost":5.00,"source":"APP"}]}`

**Fee Deletion Approach:**
- **Primary Method**: BigCommerce bulk deletion API with specific fee IDs
- **Endpoint**: `DELETE /stores/{store_hash}/v3/checkouts/{checkoutId}/fees` with `{"ids": ["fee_id"]}`
- **Fallback Method**: If bulk deletion fails, fetch all fees, remove target fee, update with remaining fees
- **Verification**: System verifies fee removal after deletion attempt

**404 Error Handling:**
- **404 on Checkout**: Treated as successful (checkout completed/expired)
- **404 on Fee**: Treated as successful (fee already removed)
- **Graceful Degradation**: Synchronization continues even when checkouts are inaccessible

### Required BigCommerce Permissions

The ACCESS_TOKEN must have the following scopes:
- `store_v2_checkouts_read`
- `store_v2_checkouts_modify`
