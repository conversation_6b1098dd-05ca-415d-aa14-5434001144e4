import db from './db';
import { getCheckoutDetails, applyCheckoutFee, calculateFeeCost, getProductDetails } from './bigcommerce-api';
import { FeeRuleData } from '../types/db';
import { FeeRequest, FeeResponse, CheckoutData, ProductData } from '../types/bigcommerce';

export interface FeeApplicationResult {
    success: boolean;
    appliedFees: FeeResponse[];
    errors: Array<{
        feeRule: FeeRuleData;
        error: string;
    }>;
    trackingUpdates?: Array<{
        feeRuleId: string;
        feeRuleName: string;
        bigCommerceFeeId: string;
        success: boolean;
        error?: string;
    }>;
}

/**
 * Get all active fee rules for a store
 */
export async function getActiveFeeRules(storeHash: string): Promise<FeeRuleData[]> {
    try {
        const allFeeRules = await db.getFeeRules(storeHash);
        return allFeeRules.filter(rule => rule.active === true);
    } catch (error) {
        console.error('Error fetching active fee rules:', error);
        throw new Error(`Failed to fetch fee rules: ${error.message}`);
    }
}

/**
 * Validate BigCommerce fee request before sending to API
 */
function validateFeeRequest(feeRequest: FeeRequest): { valid: boolean; error?: string } {
    if (!feeRequest.name || !feeRequest.name.trim()) {
        return { valid: false, error: 'Fee name is required' };
    }

    if (!feeRequest.display_name || !feeRequest.display_name.trim()) {
        return { valid: false, error: 'Fee display_name is required' };
    }

    if (typeof feeRequest.cost !== 'number' || feeRequest.cost < 0) {
        return { valid: false, error: 'Fee cost must be a non-negative number' };
    }

    if (!feeRequest.source || !feeRequest.source.trim()) {
        return { valid: false, error: 'Fee source is required by BigCommerce API' };
    }

    if (feeRequest.type !== 'custom_fee') {
        return { valid: false, error: 'Fee type must be "custom_fee"' };
    }

    return { valid: true };
}

/**
 * Convert a fee rule to BigCommerce fee request format
 */
export function convertFeeRuleToRequest(
    feeRule: FeeRuleData,
    calculatedCost: number
): FeeRequest {
    const feeRequest: FeeRequest = {
        type: 'custom_fee',
        name: feeRule.name,
        display_name: feeRule.display_name,
        cost: calculatedCost,
        // BigCommerce API requires source field - provide default if not specified
        source: feeRule.source || 'APP',
    };

    // Add optional fields if they exist
    if (feeRule.tax_class_id !== undefined && feeRule.tax_class_id !== null) {
        feeRequest.tax_class_id = feeRule.tax_class_id;
    }

    // Validate the fee request before returning
    const validation = validateFeeRequest(feeRequest);
    if (!validation.valid) {
        throw new Error(`Invalid fee request: ${validation.error}`);
    }

    return feeRequest;
}

/**
 * Check if a fee with the same name already exists in the checkout
 */
async function checkForExistingFees(storeHash: string, checkoutId: string): Promise<FeeResponse[]> {
    try {
        const checkoutData = await getCheckoutDetails(storeHash, checkoutId);
        return checkoutData.fees || [];
    } catch (error) {
        console.warn('Could not fetch existing fees for duplicate check:', error);
        return [];
    }
}

/**
 * Calculate filtered fee amount based on comprehensive product matching criteria
 *
 * This function implements comprehensive fee calculation logic:
 * 1. No Filters: Uses cart_amount_inc_tax (default behavior)
 * 2. Product Filters: Sums price of products matching ANY of the following criteria:
 *    - Product is directly selected in selectedProducts array
 *    - Product's brand matches any selectedBrands
 *    - Product's category matches any selectedCategories
 * 3. Prevents double-counting: Each product is counted only once even if it matches multiple criteria
 * 4. Price Calculation: Uses sale_price when valid (> 0), falls back to regular price if sale_price is 0, null, or undefined
 *
 * @param storeHash - BigCommerce store hash for API authentication
 * @param checkoutData - Checkout data containing line items
 * @param feeRule - Fee rule with selectedProducts, selectedBrands, and selectedCategories filters
 * @returns Filtered total amount for percentage fee calculation (using sale prices when valid, regular prices otherwise)
 */
export async function calculateFilteredFeeAmount(
    storeHash: string,
    checkoutData: CheckoutData,
    feeRule: FeeRuleData
): Promise<number> {
    const selectedProducts = feeRule.selectedProducts || [];
    const selectedBrands = feeRule.selectedBrands || [];
    const selectedCategories = feeRule.selectedCategories || [];

    // If no product filters are selected, use default behavior (cart_amount_inc_tax)
    if (selectedProducts.length === 0 && selectedBrands.length === 0 && selectedCategories.length === 0) {
        return checkoutData?.cart?.cart_amount_inc_tax || 0;
    }

    // Get physical items from checkout
    const physicalItems = checkoutData?.cart?.line_items?.physical_items || [];

    if (physicalItems.length === 0) {
        console.log('No physical items found in checkout for filtered fee calculation');
        return 0;
    }

    console.log(`Calculating filtered fee for ${physicalItems.length} physical items`);
    console.log(`Selected products: [${selectedProducts.join(', ')}]`);
    console.log(`Selected brands: [${selectedBrands.join(', ')}]`);
    console.log(`Selected categories: [${selectedCategories.join(', ')}]`);

    let filteredTotal = 0;
    const processedProductIds = new Set<number>(); // Track processed products to prevent double-counting

    // Process each physical item with comprehensive matching logic
    for (const item of physicalItems) {
        try {
            // Skip if we've already processed this product (prevent double-counting)
            if (processedProductIds.has(item.product_id)) {
                console.log(`Skipping product ${item.product_id} - already processed to prevent double-counting`);
                continue;
            }

            // Check if product matches any criteria
            let includeProduct = false;
            let matchReason = '';

            // Check direct product selection first (most specific)
            if (selectedProducts.includes(item.product_id)) {
                includeProduct = true;
                matchReason = 'directly selected';
                console.log(`Product ${item.product_id} matches: ${matchReason}`);
            } else {
                // Get product details to check brand and categories
                const productData = await getProductDetails(storeHash, item.product_id);

                // Check brand matching
                const brandMatches = selectedBrands.length > 0 && selectedBrands.includes(productData.brand_id);

                // Check category matching
                const categoryMatches = selectedCategories.length > 0 &&
                    productData.categories.some(catId => selectedCategories.includes(catId));

                // Product matches if it meets ANY criteria (OR logic)
                includeProduct = brandMatches || categoryMatches;

                // Build match reason for logging
                const matchReasons = [];
                if (brandMatches) matchReasons.push(`brand_id=${productData.brand_id}`);
                if (categoryMatches) matchReasons.push(`categories=[${productData.categories.join(', ')}]`);
                matchReason = matchReasons.join(' OR ');

                console.log(`Product ${item.product_id}: brand_id=${productData.brand_id}, categories=[${productData.categories.join(', ')}], brandMatches=${brandMatches}, categoryMatches=${categoryMatches}, included=${includeProduct}`);
            }

            if (includeProduct) {
                // Mark product as processed to prevent double-counting
                processedProductIds.add(item.product_id);

                // Get product details for price calculation if not already fetched
                let productData: any;
                let effectivePrice: number;

                if (selectedProducts.includes(item.product_id)) {
                    // For directly selected products, we need to fetch product details for price
                    productData = await getProductDetails(storeHash, item.product_id);
                    
                } else {
                    // Product data already fetched above for brand/category checking
                    productData = await getProductDetails(storeHash, item.product_id);
                }

                // Use sale_price only if it's valid (not null, not undefined, and greater than 0)
                // Fall back to regular price if sale_price is null, undefined, or 0
                const hasSalePrice = productData.sale_price !== null &&
                                   productData.sale_price !== undefined &&
                                   productData.sale_price > 0;

                effectivePrice = hasSalePrice ? productData.sale_price : productData.price;

                const productPrice = effectivePrice * item.quantity;
                filteredTotal += productPrice;

                // Enhanced logging to show which price was used and why
                let priceType: string;
                if (hasSalePrice) {
                    priceType = 'sale_price';
                } else if (productData.sale_price === 0) {
                    priceType = 'price (sale_price=0, using fallback)';
                } else {
                    priceType = 'price (no sale_price)';
                }
                console.log(`Added product ${item.product_id} (${item.name}): ${priceType}=${effectivePrice} x quantity=${item.quantity} = ${productPrice} (${matchReason})`);
            }

        } catch (error) {
            console.error(`Error fetching product details for product ${item.product_id}:`, error);
            // Continue with other products if one fails
            // This ensures that if one product API call fails, the fee calculation can still proceed
        }
    }

    console.log(`Filtered total for fee calculation: ${filteredTotal}`);
    return filteredTotal;
}

/**
 * Calculate fee cost with comprehensive product matching support for synchronization
 * This function combines filtered calculation with the final fee cost calculation
 *
 * @param storeHash - BigCommerce store hash for API authentication
 * @param checkoutData - Checkout data containing line items
 * @param feeRule - Fee rule with selectedProducts, selectedBrands, and selectedCategories filters
 * @returns Final calculated fee cost (0 if no products match filters)
 */
export async function calculateFilteredFeeCost(
    storeHash: string,
    checkoutData: CheckoutData,
    feeRule: FeeRuleData
): Promise<number> {
    let baseAmount: number;

    if (feeRule.type === 'percentage') {
        try {
            // For percentage fees, use comprehensive product matching with sale price preference
            baseAmount = await calculateFilteredFeeAmount(storeHash, checkoutData, feeRule);
        } catch (error) {
            console.error(`Error in filtered fee calculation for "${feeRule.name}", falling back to cart total:`, error);
            // Fallback to default behavior if filtered calculation fails
            baseAmount = checkoutData?.cart?.cart_amount_inc_tax || 0;
        }
    } else {
        // For fixed fees, the base amount doesn't matter as the cost is fixed
        baseAmount = 0;
    }

    return calculateFeeCost(
        feeRule.type,
        feeRule.cost,
        baseAmount
    );
}

/**
 * Apply all active fee rules to a checkout with comprehensive matching logic
 *
 * Fee rules apply when ALL conditions are met:
 * - Customer group matches exactly (if specified in fee rule)
 * - Channel ID matches exactly (if specified in fee rule)
 * - At least ONE product criteria matches (products OR brands OR categories)
 *
 * @param storeHash - BigCommerce store hash for API authentication
 * @param checkoutId - BigCommerce checkout ID to apply fees to
 * @param customer - Optional customer data for group filtering
 * @param channel - Optional channel data for channel filtering
 * @returns Promise<FeeApplicationResult> - Results of fee application process
 */
export async function applyFeesToCheckout(
    storeHash: string,
    checkoutId: string,
    customer?: any,
    channel?: any
): Promise<FeeApplicationResult> {
    const result: FeeApplicationResult = {
        success: true,
        appliedFees: [],
        errors: [],
        trackingUpdates: [],
    };

    try {
        // Get active fee rules
        const activeFeeRules = await getActiveFeeRules(storeHash);

        if (activeFeeRules.length === 0) {
            console.log(`No active fee rules found for store ${storeHash}`);
            return result;
        }

        console.log(`Found ${activeFeeRules.length} active fee rules for store ${storeHash}`);

        // If customer data is not provided, try to fetch it from the database
        let customerData = customer;
        if (!customerData) {
            try {
                // Fetch checkout data from database to get customer information
                const checkoutData = await db.getRecentCheckoutIds(storeHash, 1)
                    .then(checkouts => checkouts.length > 0 ? checkouts[0] : null);
                if (checkoutData && checkoutData.customer) {
                    customerData = checkoutData.customer;
                    // console.log(`Retrieved customer data from database for checkout ${checkoutId}:`, customerData);
                } else {
                    console.log(`No customer data found in database for checkout ${checkoutId}`);
                }
            } catch (error) {
                console.warn(`Failed to retrieve customer data from database for checkout ${checkoutId}:`, error);
                // Continue without customer data - fees with customer group filtering will be skipped
            }
        }

        // Check for existing fees to prevent duplicates
        const existingFees = await checkForExistingFees(storeHash, checkoutId);
        console.log(`Found ${existingFees.length} existing fees in checkout`);

        // Get checkout details for percentage calculations and fee application
        let checkoutData: CheckoutData | undefined;
        try {
            checkoutData = await getCheckoutDetails(storeHash, checkoutId);
            console.log(`Checkout ${checkoutId} grand total: ${checkoutData.grand_total}`);
        } catch (error) {
            console.error('Error fetching checkout details:', error);
            result.success = false;
            result.errors.push({
                feeRule: null,
                error: `Failed to fetch checkout details: ${error.message}`,
            });
            return result;
        }

        // Apply each fee rule
        for (const feeRule of activeFeeRules) {
            try {
                // Check if fee with same name already exists
                const existingFee = existingFees.find(fee => fee.name === feeRule.name);
                if (existingFee) {
                    console.log(`Skipping fee "${feeRule.name}" - already exists with ID: ${existingFee.id}`);
                    continue;
                }

                // Validate fee rule before applying
                const validation = validateFeeRule(feeRule);
                if (!validation.valid) {
                    throw new Error(`Invalid fee rule: ${validation.error}`);
                }

                // Check customer group filtering
                const customerGroupId = customerData?.customer_group_id;
                const selectedCustomerGroups = feeRule.selectedCustomerGroups || [];

                // console.log(`Customer data for checkout ${checkoutId}:`, customerData);
                console.log(`Selected customer groups for fee "${feeRule.name}":`, selectedCustomerGroups);

                // Skip fee if customer groups are specified but:
                // 1. No customer data is available, OR
                // 2. Customer's group is not in the selected groups
                if (selectedCustomerGroups.length > 0) {
                    if (!customerGroupId) {
                        console.log(`Skipping fee "${feeRule.name}" - customer data not available but fee requires specific customer groups`);
                        continue;
                    }

                    if (!selectedCustomerGroups.includes(customerGroupId)) {
                        console.log(`Skipping fee "${feeRule.name}" - customer group ${customerGroupId} not in selected groups [${selectedCustomerGroups.join(', ')}]`);
                        continue;
                    }

                    console.log(`Customer group ${customerGroupId} matches fee rule "${feeRule.name}" selected groups [${selectedCustomerGroups.join(', ')}]`);
                }

                // Check channel ID filtering
                let channelData = channel;
                let channelId = channelData?.id || channelData?.channel_id;

                // If channel data is not provided, try to fetch it from the database
                if (!channelId) {
                    try {
                        // Fetch checkout data from database to get channel information
                        const checkoutRecord = await db.getRecentCheckoutIds(storeHash, 1)
                            .then(checkouts => checkouts.find(c => c.checkoutId === checkoutId));
                        if (checkoutRecord && checkoutRecord.channel) {
                            channelData = checkoutRecord.channel;
                            channelId = channelData?.id || channelData?.channel_id;
                            console.log(`Retrieved channel data from database for checkout ${checkoutId}:`, channelData);
                        } else {
                            console.log(`No channel data found in database for checkout ${checkoutId}`);
                        }
                    } catch (error) {
                        console.warn(`Failed to retrieve channel data from database for checkout ${checkoutId}:`, error);
                        // Continue without channel data - fees with channel filtering will be skipped
                    }
                }

                const selectedChannels = feeRule.selectedChannels || [];
                console.log(`Channel ID: ${channelId}, Selected channels for fee "${feeRule.name}":`, selectedChannels);

                // Skip fee if channels are specified but:
                // 1. No channel data is available, OR
                // 2. Channel ID is not in the selected channels
                if (selectedChannels.length > 0) {
                    if (!channelId) {
                        console.log(`Skipping fee "${feeRule.name}" - channel data not available but fee requires specific channels`);
                        continue;
                    }

                    if (!selectedChannels.includes(channelId)) {
                        console.log(`Skipping fee "${feeRule.name}" - channel ID ${channelId} not in selected channels [${selectedChannels.join(', ')}]`);
                        continue;
                    }

                    console.log(`Channel ID ${channelId} matches fee rule "${feeRule.name}" selected channels [${selectedChannels.join(', ')}]`);
                }

                // Check product criteria matching (must happen for ALL fee types)
                const selectedProducts = feeRule.selectedProducts || [];
                const selectedBrands = feeRule.selectedBrands || [];
                const selectedCategories = feeRule.selectedCategories || [];

                // If product filters are specified, validate that at least one product matches
                if (selectedProducts.length > 0 || selectedBrands.length > 0 || selectedCategories.length > 0) {
                    console.log(`Checking product criteria for fee "${feeRule.name}": products=[${selectedProducts.join(', ')}], brands=[${selectedBrands.join(', ')}], categories=[${selectedCategories.join(', ')}]`);

                    const physicalItems = checkoutData?.cart?.line_items?.physical_items || [];
                    let hasMatchingProduct = false;

                    for (const item of physicalItems) {
                        try {
                            // Check direct product selection first
                            if (selectedProducts.includes(item.product_id)) {
                                hasMatchingProduct = true;
                                console.log(`Product ${item.product_id} matches: directly selected`);
                                break;
                            }

                            // Check brand and category matching
                            const productData = await getProductDetails(storeHash, item.product_id);

                            const brandMatches = selectedBrands.length > 0 && selectedBrands.includes(productData.brand_id);
                            const categoryMatches = selectedCategories.length > 0 &&
                                productData.categories.some(catId => selectedCategories.includes(catId));

                            if (brandMatches || categoryMatches) {
                                hasMatchingProduct = true;
                                const matchReasons = [];
                                if (brandMatches) matchReasons.push(`brand_id=${productData.brand_id}`);
                                if (categoryMatches) matchReasons.push(`categories=[${productData.categories.join(', ')}]`);
                                console.log(`Product ${item.product_id} matches: ${matchReasons.join(' OR ')}`);
                                break;
                            }
                        } catch (error) {
                            console.error(`Error checking product ${item.product_id} for fee "${feeRule.name}":`, error);
                            // Continue checking other products
                        }
                    }

                    if (!hasMatchingProduct) {
                        console.log(`Skipping fee "${feeRule.name}" - no products match the selected product/brand/category criteria`);
                        continue;
                    }

                    console.log(`Product criteria validation passed for fee "${feeRule.name}"`);
                }

                // Calculate the fee cost using filtered amount or cart amount including tax
                let baseAmount: number;

                if (feeRule.type === 'percentage') {
                    try {
                        // For percentage fees, use comprehensive product matching (products, brands, categories)
                        baseAmount = await calculateFilteredFeeAmount(storeHash, checkoutData, feeRule);
                    } catch (error) {
                        console.error(`Error in filtered fee calculation for "${feeRule.name}", falling back to cart total:`, error);
                        // Fallback to default behavior if filtered calculation fails
                        baseAmount = checkoutData?.cart?.cart_amount_inc_tax || 0;
                    }
                } else {
                    // For fixed fees, the base amount doesn't matter as the cost is fixed
                    baseAmount = 0;
                }

                const calculatedCost = calculateFeeCost(
                    feeRule.type,
                    feeRule.cost,
                    baseAmount
                );

                console.log(`Calculated fee "${feeRule.name}" (${feeRule.type}): ${calculatedCost} (base amount: ${baseAmount})`);

                // Skip applying fee if calculated cost is 0 or very close to 0 (BigCommerce API doesn't accept 0-cost fees)
                if (calculatedCost === 0 || calculatedCost < 0.01) {
                    if (feeRule.type === 'percentage') {
                        if (baseAmount === 0) {
                            console.log(`Skipping fee "${feeRule.name}" - calculated cost is ${calculatedCost} (no products match the selected product/brand/category filters or all matching products have zero prices)`);
                        } else {
                            console.log(`Skipping fee "${feeRule.name}" - calculated cost is ${calculatedCost} (percentage fee resulted in very small amount)`);
                        }
                    } else {
                        console.log(`Skipping fee "${feeRule.name}" - calculated cost is ${calculatedCost} (fixed fee with 0 or very small cost)`);
                    }
                    continue; // Skip to next fee rule without counting as error
                }

                console.log(`Applying fee "${feeRule.name}" to checkout`);

                // Convert to BigCommerce fee request format
                const feeRequest = convertFeeRuleToRequest(feeRule, calculatedCost);
                console.log(`Fee request for "${feeRule.name}":`, JSON.stringify(feeRequest, null, 2));

                // Apply the fee
                const appliedFee = await applyCheckoutFee(storeHash, checkoutId, feeRequest);

                if (!appliedFee || !appliedFee.id) {
                    throw new Error(`Fee application returned invalid response: ${JSON.stringify(appliedFee)}`);
                }

                result.appliedFees.push(appliedFee);

                // Log with proper cost value (use cost_inc_tax if available, fallback to cost)
                const displayCost = appliedFee.cost_inc_tax || appliedFee.cost || 'unknown';
                console.log(`Successfully applied fee "${feeRule.name}" with ID: ${appliedFee.id}, cost: ${displayCost}`);

                // Track the applied fee in the database
                try {
                    if (feeRule.id) {
                        await db.updateFeeRuleTracking(
                            storeHash,
                            feeRule.id,
                            appliedFee.id,
                            checkoutId,
                            appliedFee.cost_inc_tax || appliedFee.cost_ex_tax || appliedFee.cost || calculatedCost
                        );
                        console.log(`Updated fee rule ${feeRule.id} tracking with BigCommerce fee ID: ${appliedFee.id}`);

                        // Record successful tracking update
                        result.trackingUpdates?.push({
                            feeRuleId: feeRule.id,
                            feeRuleName: feeRule.name,
                            bigCommerceFeeId: appliedFee.id,
                            success: true
                        });
                    } else {
                        console.warn(`Fee rule "${feeRule.name}" has no ID, cannot update tracking`);

                        // Record failed tracking update
                        result.trackingUpdates?.push({
                            feeRuleId: 'unknown',
                            feeRuleName: feeRule.name,
                            bigCommerceFeeId: appliedFee.id,
                            success: false,
                            error: 'Fee rule has no ID'
                        });
                    }
                } catch (trackingError) {
                    console.error(`Error updating fee tracking for rule "${feeRule.name}":`, trackingError);

                    // Record failed tracking update
                    result.trackingUpdates?.push({
                        feeRuleId: feeRule.id || 'unknown',
                        feeRuleName: feeRule.name,
                        bigCommerceFeeId: appliedFee.id,
                        success: false,
                        error: trackingError.message || 'Unknown tracking error'
                    });

                    // Don't fail the entire process if tracking fails
                }

            } catch (error) {
                console.error(`Error applying fee rule "${feeRule.name}":`, error);
                result.success = false;

                // Extract detailed error information from BigCommerce API errors
                let errorMessage = 'Unknown error occurred';
                if (error.status && error.title) {
                    // BigCommerce API error
                    errorMessage = `BigCommerce API Error (${error.status}): ${error.title}`;
                    if (error.detail) {
                        errorMessage += ` - ${error.detail}`;
                    }
                    if (error.errors) {
                        const errorDetails = Object.entries(error.errors)
                            .map(([field, message]) => `${field}: ${message}`)
                            .join(', ');
                        errorMessage += ` (${errorDetails})`;
                    }
                } else if (error.message) {
                    errorMessage = error.message;
                }

                result.errors.push({
                    feeRule,
                    error: errorMessage,
                });
            }
        }

        const totalFeeRules = activeFeeRules.length;
        const appliedCount = result.appliedFees.length;
        const errorCount = result.errors.length;
        const skippedCount = totalFeeRules - appliedCount - errorCount;

        console.log(`Fee application completed. Total rules: ${totalFeeRules}, Applied: ${appliedCount}, Skipped: ${skippedCount}, Errors: ${errorCount}`);

    } catch (error) {
        console.error('Error in fee application process:', error);
        result.success = false;
        result.errors.push({
            feeRule: null,
            error: `Fee application process failed: ${error.message}`,
        });
    }

    return result;
}

/**
 * Apply a single fee rule to the most recent checkout with comprehensive matching logic
 * Used when fee rules are created or edited to automatically apply them
 *
 * Fee rule applies when ALL conditions are met:
 * - Customer group matches exactly (if specified in fee rule)
 * - Channel ID matches exactly (if specified in fee rule)
 * - At least ONE product criteria matches (products OR brands OR categories)
 *
 * @param storeHash - BigCommerce store hash for API authentication
 * @param feeRule - Fee rule to apply with comprehensive matching criteria
 * @returns Promise<FeeApplicationResult> - Results of fee application process
 */
export async function applyFeeRuleToMostRecentCheckout(
    storeHash: string,
    feeRule: FeeRuleData
): Promise<FeeApplicationResult> {
    const result: FeeApplicationResult = {
        success: true,
        appliedFees: [],
        errors: [],
        trackingUpdates: [],
    };

    try {
        // Get the most recent checkout ID
        const recentCheckouts = await db.getRecentCheckoutIds(storeHash, 1);

        if (recentCheckouts.length === 0) {
            console.log(`No recent checkouts found for store ${storeHash} - skipping automatic fee application`);
            return result;
        }

        const mostRecentCheckout = recentCheckouts[0];
        const checkoutId = mostRecentCheckout.checkoutId;
        const customer = mostRecentCheckout.customer;
        const channel = mostRecentCheckout.channel;

        console.log(`Applying fee rule "${feeRule.name}" to most recent checkout: ${checkoutId}`);

        // Validate fee rule before applying
        const validation = validateFeeRule(feeRule);
        if (!validation.valid) {
            throw new Error(`Invalid fee rule: ${validation.error}`);
        }

        // Check if fee rule is active
        if (!feeRule.active) {
            console.log(`Skipping inactive fee rule "${feeRule.name}"`);
            return result;
        }

        // Check customer group filtering
        const customerGroupId = customer?.customer_group_id;
        const selectedCustomerGroups = feeRule.selectedCustomerGroups || [];

        console.log(`Customer group ID: ${customerGroupId}, Selected customer groups: [${selectedCustomerGroups.join(', ')}]`);

        // Skip fee if customer groups are specified but don't match
        if (selectedCustomerGroups.length > 0) {
            if (!customerGroupId) {
                console.log(`Skipping fee "${feeRule.name}" - customer data not available but fee requires specific customer groups`);
                return result;
            }

            if (!selectedCustomerGroups.includes(customerGroupId)) {
                console.log(`Skipping fee "${feeRule.name}" - customer group ${customerGroupId} not in selected groups [${selectedCustomerGroups.join(', ')}]`);
                return result;
            }

            console.log(`Customer group ${customerGroupId} matches fee rule "${feeRule.name}" selected groups [${selectedCustomerGroups.join(', ')}]`);
        }

        // Check channel ID filtering
        const channelId = channel?.id || channel?.channel_id;
        const selectedChannels = feeRule.selectedChannels || [];

        console.log(`Channel ID: ${channelId}, Selected channels for fee "${feeRule.name}":`, selectedChannels);

        // Skip fee if channels are specified but don't match
        if (selectedChannels.length > 0) {
            if (!channelId) {
                console.log(`Skipping fee "${feeRule.name}" - channel data not available but fee requires specific channels`);
                return result;
            }

            if (!selectedChannels.includes(channelId)) {
                console.log(`Skipping fee "${feeRule.name}" - channel ID ${channelId} not in selected channels [${selectedChannels.join(', ')}]`);
                return result;
            }

            console.log(`Channel ID ${channelId} matches fee rule "${feeRule.name}" selected channels [${selectedChannels.join(', ')}]`);
        }

        // Get checkout details for fee calculation
        let checkoutData: CheckoutData;
        try {
            checkoutData = await getCheckoutDetails(storeHash, checkoutId);
            console.log(`Checkout ${checkoutId} grand total: ${checkoutData.grand_total}`);
        } catch (error) {
            console.error('Error fetching checkout details:', error);
            result.success = false;
            result.errors.push({
                feeRule,
                error: `Failed to fetch checkout details: ${error.message}`,
            });
            return result;
        }

        // Check for existing fees to prevent duplicates
        const existingFees = await checkForExistingFees(storeHash, checkoutId);
        const existingFee = existingFees.find(fee => fee.name === feeRule.name);
        if (existingFee) {
            console.log(`Skipping fee "${feeRule.name}" - already exists with ID: ${existingFee.id}`);
            return result;
        }

        // Check product criteria matching (must happen for ALL fee types)
        const selectedProducts = feeRule.selectedProducts || [];
        const selectedBrands = feeRule.selectedBrands || [];
        const selectedCategories = feeRule.selectedCategories || [];

        // If product filters are specified, validate that at least one product matches
        if (selectedProducts.length > 0 || selectedBrands.length > 0 || selectedCategories.length > 0) {
            console.log(`Checking product criteria for fee "${feeRule.name}": products=[${selectedProducts.join(', ')}], brands=[${selectedBrands.join(', ')}], categories=[${selectedCategories.join(', ')}]`);

            const physicalItems = checkoutData?.cart?.line_items?.physical_items || [];
            let hasMatchingProduct = false;

            for (const item of physicalItems) {
                try {
                    // Check direct product selection first
                    if (selectedProducts.includes(item.product_id)) {
                        hasMatchingProduct = true;
                        console.log(`Product ${item.product_id} matches: directly selected`);
                        break;
                    }

                    // Check brand and category matching
                    const productData = await getProductDetails(storeHash, item.product_id);

                    const brandMatches = selectedBrands.length > 0 && selectedBrands.includes(productData.brand_id);
                    const categoryMatches = selectedCategories.length > 0 &&
                        productData.categories.some(catId => selectedCategories.includes(catId));

                    if (brandMatches || categoryMatches) {
                        hasMatchingProduct = true;
                        const matchReasons = [];
                        if (brandMatches) matchReasons.push(`brand_id=${productData.brand_id}`);
                        if (categoryMatches) matchReasons.push(`categories=[${productData.categories.join(', ')}]`);
                        console.log(`Product ${item.product_id} matches: ${matchReasons.join(' OR ')}`);
                        break;
                    }
                } catch (error) {
                    console.error(`Error checking product ${item.product_id} for fee "${feeRule.name}":`, error);
                    // Continue checking other products
                }
            }

            if (!hasMatchingProduct) {
                console.log(`Skipping fee "${feeRule.name}" - no products match the selected product/brand/category criteria`);
                return result;
            }

            console.log(`Product criteria validation passed for fee "${feeRule.name}"`);
        }

        // Calculate the fee cost using comprehensive product matching
        let baseAmount: number;

        if (feeRule.type === 'percentage') {
            try {
                // For percentage fees, use filtered calculation based on comprehensive product matching
                baseAmount = await calculateFilteredFeeAmount(storeHash, checkoutData, feeRule);
            } catch (error) {
                console.error(`Error in filtered fee calculation for "${feeRule.name}", falling back to cart total:`, error);
                // Fallback to default behavior if filtered calculation fails
                baseAmount = checkoutData?.cart?.cart_amount_inc_tax || 0;
            }
        } else {
            // For fixed fees, the base amount doesn't matter as the cost is fixed
            baseAmount = 0;
        }

        const calculatedCost = calculateFeeCost(
            feeRule.type,
            feeRule.cost,
            baseAmount
        );

        console.log(`Calculated fee "${feeRule.name}" (${feeRule.type}): ${calculatedCost} (base amount: ${baseAmount})`);

        // Skip applying fee if calculated cost is 0 or very close to 0 (BigCommerce API doesn't accept 0-cost fees)
        if (calculatedCost === 0 || calculatedCost < 0.01) {
            if (feeRule.type === 'percentage') {
                if (baseAmount === 0) {
                    console.log(`Skipping fee "${feeRule.name}" - calculated cost is ${calculatedCost} (no products match the selected filters or all matching products have zero prices)`);
                } else {
                    console.log(`Skipping fee "${feeRule.name}" - calculated cost is ${calculatedCost} (percentage fee resulted in very small amount)`);
                }
            } else {
                console.log(`Skipping fee "${feeRule.name}" - calculated cost is ${calculatedCost} (fixed fee with 0 or very small cost)`);
            }
            return result; // Return success but with no applied fees
        }

        console.log(`Applying fee "${feeRule.name}" to checkout ${checkoutId}`);

        // Convert to BigCommerce fee request format
        const feeRequest = convertFeeRuleToRequest(feeRule, calculatedCost);
        console.log(`Fee request for "${feeRule.name}":`, JSON.stringify(feeRequest, null, 2));

        // Apply the fee
        const appliedFee = await applyCheckoutFee(storeHash, checkoutId, feeRequest);

        if (!appliedFee || !appliedFee.id) {
            throw new Error(`Fee application returned invalid response: ${JSON.stringify(appliedFee)}`);
        }

        result.appliedFees.push(appliedFee);

        // Log with proper cost value (use cost_inc_tax if available, fallback to cost)
        const displayCost = appliedFee.cost_inc_tax || appliedFee.cost || 'unknown';
        console.log(`Successfully applied fee "${feeRule.name}" with ID: ${appliedFee.id}, cost: ${displayCost}`);

        // Track the applied fee in the database
        try {
            if (feeRule.id) {
                await db.updateFeeRuleTracking(
                    storeHash,
                    feeRule.id,
                    appliedFee.id,
                    checkoutId,
                    appliedFee.cost_inc_tax || appliedFee.cost_ex_tax || appliedFee.cost || calculatedCost
                );
                console.log(`Updated fee rule ${feeRule.id} tracking with BigCommerce fee ID: ${appliedFee.id}`);

                // Record successful tracking update
                result.trackingUpdates?.push({
                    feeRuleId: feeRule.id,
                    feeRuleName: feeRule.name,
                    bigCommerceFeeId: appliedFee.id,
                    success: true
                });
            } else {
                console.warn(`Fee rule "${feeRule.name}" has no ID, cannot update tracking`);

                // Record failed tracking update
                result.trackingUpdates?.push({
                    feeRuleId: 'unknown',
                    feeRuleName: feeRule.name,
                    bigCommerceFeeId: appliedFee.id,
                    success: false,
                    error: 'Fee rule has no ID'
                });
            }
        } catch (trackingError) {
            console.error(`Error updating fee tracking for rule "${feeRule.name}":`, trackingError);

            // Record failed tracking update
            result.trackingUpdates?.push({
                feeRuleId: feeRule.id || 'unknown',
                feeRuleName: feeRule.name,
                bigCommerceFeeId: appliedFee.id,
                success: false,
                error: trackingError.message || 'Unknown tracking error'
            });

            // Don't fail the entire process if tracking fails
        }

        console.log(`Fee rule "${feeRule.name}" successfully applied to most recent checkout ${checkoutId}`);

    } catch (error) {
        console.error(`Error applying fee rule "${feeRule.name}" to most recent checkout:`, error);
        result.success = false;

        // Extract detailed error information from BigCommerce API errors
        let errorMessage = 'Unknown error occurred';
        if (error.status && error.title) {
            // BigCommerce API error
            errorMessage = `BigCommerce API Error (${error.status}): ${error.title}`;
            if (error.detail) {
                errorMessage += ` - ${error.detail}`;
            }
            if (error.errors) {
                const errorDetails = Object.entries(error.errors)
                    .map(([field, message]) => `${field}: ${message}`)
                    .join(', ');
                errorMessage += ` (${errorDetails})`;
            }
        } else if (error.message) {
            errorMessage = error.message;
        }

        result.errors.push({
            feeRule,
            error: errorMessage,
        });
    }

    return result;
}

/**
 * Validate fee rule before application
 */
export function validateFeeRule(feeRule: FeeRuleData): { valid: boolean; error?: string } {
    if (!feeRule.name || !feeRule.display_name) {
        return { valid: false, error: 'Fee rule must have name and display_name' };
    }

    if (!['percentage', 'fixed'].includes(feeRule.type)) {
        return { valid: false, error: 'Fee rule type must be "percentage" or "fixed"' };
    }

    if (typeof feeRule.cost !== 'number' || feeRule.cost < 0) {
        return { valid: false, error: 'Fee rule cost must be a non-negative number' };
    }

    if (feeRule.type === 'percentage' && feeRule.cost > 100) {
        return { valid: false, error: 'Percentage fee cannot exceed 100%' };
    }

    // Note: source field is not required in fee rule as we provide a default in convertFeeRuleToRequest
    // This allows existing fee rules without source to continue working

    return { valid: true };
}
