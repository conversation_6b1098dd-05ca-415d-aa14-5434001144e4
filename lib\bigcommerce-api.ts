import { CheckoutData, FeeRequest, FeeRequestBody, FeeDeletionRequestBody, FeeResponse, BigCommerceApiError, ProductData } from '../types/bigcommerce';

const { ACCESS_TOKEN } = process.env;

if (!ACCESS_TOKEN) {
    throw new Error('ACCESS_TOKEN environment variable is required');
}

/**
 * Base BigCommerce API client for making authenticated requests
 */
class BigCommerceApiClient {
    private baseUrl: string;
    private headers: Record<string, string>;

    constructor(storeHash: string) {
        this.baseUrl = `https://api.bigcommerce.com/stores/${storeHash}/v3`;
        this.headers = {
            'X-Auth-Token': ACCESS_TOKEN,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        };
    }

    private async makeRequest<T>(
        endpoint: string, 
        method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
        body?: any
    ): Promise<T> {
        const url = `${this.baseUrl}${endpoint}`;
        
        const config: RequestInit = {
            method,
            headers: this.headers,
        };

        if (body && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {
            config.body = JSON.stringify(body);
        }

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error(`BigCommerce API Error (${response.status}):`, errorData);
                const error: BigCommerceApiError = {
                    status: response.status,
                    title: errorData.title || `HTTP ${response.status} Error`,
                    detail: errorData.detail || response.statusText,
                    errors: errorData.errors,
                };
                throw error;
            }

            return await response.json();
        } catch (error) {
            if (error.status) {
                // Re-throw BigCommerce API errors
                throw error;
            }
            
            // Handle network or other errors
            throw new Error(`Network error: ${error.message}`);
        }
    }

    /**
     * Fetch checkout details including cart amount and grand total
     */
    async getCheckout(checkoutId: string): Promise<CheckoutData> {
        const response = await this.makeRequest<{ data: CheckoutData }>(`/checkouts/${checkoutId}`);
        return response.data;
    }

    /**
     * Fetch product details by product ID
     */
    async getProduct(productId: number): Promise<ProductData> {
        const response = await this.makeRequest<{ data: ProductData }>(`/catalog/products/${productId}`);
        console.log("get p===========================>",response.data);
        
        return response.data;
    }

    /**
     * Apply a fee to a checkout
     */
    async applyFee(checkoutId: string, feeData: FeeRequest): Promise<FeeResponse> {
        // Wrap the fee data in the required 'fees' array format
        const requestBody: FeeRequestBody = {
            fees: [feeData]
        };

        console.log(`Applying fee to checkout ${checkoutId}:`, JSON.stringify(requestBody, null, 2));

        // The API returns the complete checkout object with fees nested inside
        const response = await this.makeRequest<{ data: CheckoutData }>(
            `/checkouts/${checkoutId}/fees`,
            'POST',
            requestBody
        );

        // console.log(`Fee application response:`, JSON.stringify(response, null, 2));

        // Extract the applied fees from the checkout response
        const appliedFees = response.data.fees || [];

        if (appliedFees.length === 0) {
            throw new Error('No fees found in checkout response after fee application');
        }

        // Find the newly applied fee by matching name and type
        // Note: We can't match by cost because API returns cost_inc_tax/cost_ex_tax instead of cost
        const matchingFees = appliedFees.filter(fee =>
            fee.name === feeData.name &&
            fee.type === feeData.type
        );

        if (matchingFees.length === 0) {
            console.warn(`Could not find any fee matching name "${feeData.name}" and type "${feeData.type}". Returning last fee in array.`);
            const lastFee = appliedFees[appliedFees.length - 1];
            // Add backward compatibility cost property
            if (lastFee && !lastFee.cost) {
                lastFee.cost = lastFee.cost_inc_tax || lastFee.cost_ex_tax || 0;
            }
            return lastFee;
        }

        // If multiple fees with same name/type, return the one with closest cost match
        let bestMatch = matchingFees[0];
        if (matchingFees.length > 1) {
            console.log(`Found ${matchingFees.length} fees with name "${feeData.name}". Selecting best cost match.`);

            // Find the fee with cost closest to our expected cost
            bestMatch = matchingFees.reduce((closest, current) => {
                const currentCost = current.cost_inc_tax || current.cost_ex_tax || 0;
                const closestCost = closest.cost_inc_tax || closest.cost_ex_tax || 0;

                const currentDiff = Math.abs(currentCost - feeData.cost);
                const closestDiff = Math.abs(closestCost - feeData.cost);

                return currentDiff < closestDiff ? current : closest;
            });
        }

        // Add backward compatibility cost property
        if (!bestMatch.cost) {
            bestMatch.cost = bestMatch.cost_inc_tax || bestMatch.cost_ex_tax || 0;
        }

        console.log(`Selected fee "${bestMatch.name}" with ID: ${bestMatch.id}, cost_inc_tax: ${bestMatch.cost_inc_tax}`);
        return bestMatch;
    }

    /**
     * Update a fee in a checkout
     * Note: BigCommerce API requires fee ID in request body, not URL path
     */
    async updateFee(checkoutId: string, feeId: string, feeData: FeeRequest): Promise<FeeResponse> {
        // Include the fee ID in the request body as required by BigCommerce API
        const feeWithId = {
            ...feeData,
            id: feeId
        };

        // Wrap the fee data in the required 'fees' array format
        const requestBody: FeeRequestBody = {
            fees: [feeWithId]
        };

        console.log(`Updating fee ${feeId} in checkout ${checkoutId}:`, JSON.stringify(requestBody, null, 2));

        // The API returns the complete checkout object with fees nested inside
        // Correct endpoint: PUT /checkouts/{checkoutId}/fees (without fee ID in URL)
        const response = await this.makeRequest<{ data: CheckoutData }>(
            `/checkouts/${checkoutId}/fees`,
            'PUT',
            requestBody
        );

        console.log(`Fee update response:`, JSON.stringify(response, null, 2));

        // Extract the updated fee from the checkout response
        const updatedFees = response.data.fees || [];

        if (updatedFees.length === 0) {
            throw new Error('No fees found in checkout response after fee update');
        }

        // Find the updated fee by ID
        const updatedFee = updatedFees.find(fee => fee.id === feeId);

        if (!updatedFee) {
            console.warn(`Could not find updated fee with ID "${feeId}". Returning last fee in array.`);
            const lastFee = updatedFees[updatedFees.length - 1];
            // Add backward compatibility cost property
            if (!lastFee.cost) {
                lastFee.cost = lastFee.cost_inc_tax || lastFee.cost_ex_tax || 0;
            }
            return lastFee;
        }

        // Add backward compatibility cost property
        if (!updatedFee.cost) {
            updatedFee.cost = updatedFee.cost_inc_tax || updatedFee.cost_ex_tax || 0;
        }

        console.log(`Successfully updated fee "${updatedFee.name}" with ID: ${updatedFee.id}`);
        return updatedFee;
    }

    /**
     * Delete specific fees from a checkout using BigCommerce bulk deletion API
     */
    async deleteFee(checkoutId: string, feeId: string): Promise<void> {
        console.log(`Deleting fee ${feeId} from checkout ${checkoutId}`);

        try {
            // Use BigCommerce bulk deletion API with specific fee ID
            const requestBody: FeeDeletionRequestBody = {
                ids: [feeId]
            };

            console.log(`Calling bulk deletion API for fee ${feeId}:`, JSON.stringify(requestBody, null, 2));

            await this.makeRequest<void>(
                `/checkouts/${checkoutId}/fees`,
                'DELETE',
                requestBody
            );

            console.log(`Successfully deleted fee ${feeId} from checkout ${checkoutId} using bulk deletion API`);

        } catch (error) {
            // If we get a 404, the checkout or fee might not exist anymore
            if (error.status === 404) {
                console.log(`Checkout ${checkoutId} or fee ${feeId} not found - considering deletion successful`);
                return;
            }

            // If bulk deletion fails with 422 or other errors, fall back to the workaround approach
            console.warn(`Bulk deletion failed for fee ${feeId} (${error.status}: ${error.title}), attempting workaround approach`);
            await this.deleteFeeWorkaround(checkoutId, feeId);
        }
    }

    /**
     * Fallback method: Delete a specific fee using workaround approach
     * This method fetches all fees, removes the target fee, and updates with remaining fees
     */
    private async deleteFeeWorkaround(checkoutId: string, feeId: string): Promise<void> {
        console.log(`Using workaround deletion for fee ${feeId} from checkout ${checkoutId}`);

        try {
            // First, get current checkout to see all fees
            const checkoutData = await this.getCheckout(checkoutId);
            const currentFees = checkoutData.fees || [];

            console.log(`Current fees in checkout:`, currentFees.map(f => ({ id: f.id, name: f.name })));

            // Find the fee to delete
            const feeToDelete = currentFees.find(fee => fee.id === feeId);
            if (!feeToDelete) {
                console.log(`Fee ${feeId} not found in checkout ${checkoutId} - may already be deleted`);
                return; // Fee doesn't exist, consider it successfully "deleted"
            }

            // Get remaining fees (all except the one to delete)
            const remainingFees = currentFees.filter(fee => fee.id !== feeId);

            console.log(`Remaining fees after deletion:`, remainingFees.map(f => ({ id: f.id, name: f.name })));

            if (remainingFees.length === 0) {
                // Delete all fees if no fees should remain
                await this.makeRequest<void>(
                    `/checkouts/${checkoutId}/fees`,
                    'DELETE'
                );
                console.log(`Successfully deleted all fees from checkout ${checkoutId}`);
            } else {
                // Update checkout with remaining fees (excluding the deleted one)
                const requestBody: FeeRequestBody = {
                    fees: remainingFees.map(fee => ({
                        id: fee.id,
                        type: fee.type as 'custom_fee',
                        name: fee.name,
                        display_name: fee.display_name || fee.name,
                        cost: fee.cost_ex_tax || fee.cost || 0,
                        source: fee.source || 'APP',
                        tax_class_id: fee.tax_class_id
                    }))
                };

                await this.makeRequest<{ data: CheckoutData }>(
                    `/checkouts/${checkoutId}/fees`,
                    'PUT',
                    requestBody
                );
                console.log(`Successfully updated checkout ${checkoutId} with remaining fees using workaround`);
            }

        } catch (error) {
            // If we get a 404, the checkout or fee might not exist anymore
            if (error.status === 404) {
                console.log(`Checkout ${checkoutId} or fee ${feeId} not found - considering deletion successful`);
                return;
            }
            throw error;
        }
    }

    /**
     * Delete multiple fees from a checkout using BigCommerce bulk deletion API
     */
    async deleteMultipleFees(checkoutId: string, feeIds: string[]): Promise<void> {
        console.log(`Deleting ${feeIds.length} fees from checkout ${checkoutId}:`, feeIds);

        if (feeIds.length === 0) {
            console.log('No fee IDs provided for deletion');
            return;
        }

        try {
            // Use BigCommerce bulk deletion API with multiple fee IDs
            const requestBody: FeeDeletionRequestBody = {
                ids: feeIds
            };

            console.log(`Calling bulk deletion API for multiple fees:`, JSON.stringify(requestBody, null, 2));

            await this.makeRequest<void>(
                `/checkouts/${checkoutId}/fees`,
                'DELETE',
                requestBody
            );

            console.log(`Successfully deleted ${feeIds.length} fees from checkout ${checkoutId} using bulk deletion API`);

        } catch (error) {
            // If we get a 404, the checkout or fees might not exist anymore
            if (error.status === 404) {
                console.log(`Checkout ${checkoutId} or fees not found - considering deletion successful`);
                return;
            }

            console.error(`Bulk deletion failed for ${feeIds.length} fees (${error.status}: ${error.title})`);
            throw error;
        }
    }
}

/**
 * Get checkout details for fee calculation
 */
export async function getCheckoutDetails(storeHash: string, checkoutId: string): Promise<CheckoutData> {
    const client = new BigCommerceApiClient(storeHash);
    return await client.getCheckout(checkoutId);
}

/**
 * Get product details for filter-based fee calculation
 */
export async function getProductDetails(storeHash: string, productId: number): Promise<ProductData> {
    const client = new BigCommerceApiClient(storeHash);
    return await client.getProduct(productId);
}

/**
 * Apply a single fee to a checkout
 */
export async function applyCheckoutFee(
    storeHash: string,
    checkoutId: string,
    feeData: FeeRequest
): Promise<FeeResponse> {
    const client = new BigCommerceApiClient(storeHash);
    return await client.applyFee(checkoutId, feeData);
}

/**
 * Update a single fee in a checkout
 */
export async function updateCheckoutFee(
    storeHash: string,
    checkoutId: string,
    feeId: string,
    feeData: FeeRequest
): Promise<FeeResponse> {
    const client = new BigCommerceApiClient(storeHash);
    return await client.updateFee(checkoutId, feeId, feeData);
}

/**
 * Delete a single fee from a checkout
 */
export async function deleteCheckoutFee(
    storeHash: string,
    checkoutId: string,
    feeId: string
): Promise<void> {
    const client = new BigCommerceApiClient(storeHash);
    return await client.deleteFee(checkoutId, feeId);
}

/**
 * Delete multiple fees from a checkout using bulk deletion
 */
export async function deleteMultipleCheckoutFees(
    storeHash: string,
    checkoutId: string,
    feeIds: string[]
): Promise<void> {
    const client = new BigCommerceApiClient(storeHash);
    return await client.deleteMultipleFees(checkoutId, feeIds);
}

/**
 * Calculate fee cost based on type and cart amount including tax
 * Note: Uses cart_amount_inc_tax instead of grand_total to prevent compounding fee calculations
 */
export function calculateFeeCost(
    feeType: 'percentage' | 'fixed',
    feeCost: number,
    cartAmountIncTax: number
): number {
    if (feeType === 'percentage') {
        // Use cart amount including tax as base for percentage calculations
        // This prevents percentage fees from being calculated on top of other fees, shipping, etc.
        return Math.round((cartAmountIncTax * feeCost / 100) * 100) / 100; // Round to 2 decimal places
    }
    return feeCost;
}
