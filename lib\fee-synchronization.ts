import db from './db';
import { getCheckoutDetails, updateCheckout<PERSON>ee, deleteCheckout<PERSON>ee, applyCheckoutFee } from './bigcommerce-api';
import { convertFeeRuleToRequest, applyFeeRuleToMostRecentCheckout, calculateFilteredFeeCost } from './fee-application';
import { FeeRuleData, AppliedFeeRecord } from '../types/db';
import { FeeResponse } from '../types/bigcommerce';

/**
 * Extract channel ID from channel data, handling both object and array formats
 * @param channelData - Channel data that can be an object, array, or undefined
 * @returns Channel ID as number or undefined
 */
function extractChannelId(channelData: any): number | undefined {
    if (!channelData) {
        return undefined;
    }

    // Handle array format: [ { id: 1 } ]
    if (Array.isArray(channelData) && channelData.length > 0) {
        const firstChannel = channelData[0];
        return firstChannel?.id || firstChannel?.channel_id;
    }

    // Handle object format: { id: 1 } or { channel_id: 1 }
    if (typeof channelData === 'object') {
        return channelData.id || channelData.channel_id;
    }

    return undefined;
}

export interface FeeSynchronizationResult {
    success: boolean;
    action: 'update' | 'delete';
    checkoutId?: string;
    bigCommerceFeeId?: string;
    error?: string;
    details?: {
        feeRuleId: string;
        feeRuleName: string;
        checkoutFound: boolean;
        feeHistoryFound: boolean;
        bigCommerceOperationSuccess: boolean;
    };
}

/**
 * Find the most recent checkout with an applied fee for a given rule
 */
async function findRecentCheckoutWithAppliedFee(storeHash: string, feeRule: FeeRuleData) {
    try {
        // Get fee rule to access its application history
        const feeRuleData = await db.getFeeRule(storeHash, feeRule.id || '');
        const feeHistory = feeRuleData?.appliedFeeHistory || [];
        
        if (!feeHistory || feeHistory.length === 0) {
            console.log(`No fee application history found for rule "${feeRule.name}"`);
            return null;
        }
        
        // Sort by appliedAt timestamp in descending order to get the most recent
        const sortedHistory = [...feeHistory].sort((a, b) => b.appliedAt - a.appliedAt);
        const mostRecentFee = sortedHistory[0];
        
        console.log(`Found most recent fee application: checkout=${mostRecentFee.checkoutId}, feeId=${mostRecentFee.feeId}`);
        
        return {
            checkoutId: mostRecentFee.checkoutId,
            feeId: mostRecentFee.feeId
        };
    } catch (error) {
        console.error(`Error finding recent checkout with applied fee:`, error);
        return null;
    }
}

/**
 * Validate that a checkout is still active and accessible
 */
async function validateCheckout(storeHash: string, checkoutId: string): Promise<boolean> {
    try {
        await getCheckoutDetails(storeHash, checkoutId);
        return true;
    } catch (error) {
        // 404 means checkout doesn't exist - this is expected for old checkouts
        if (error.status === 404) {
            console.log(`Checkout ${checkoutId} no longer exists (404) - this is normal for completed/expired checkouts`);
        } else {
            console.warn(`Checkout ${checkoutId} is no longer accessible:`, error);
        }
        return false;
    }
}

/**
 * Synchronize fee rule update with BigCommerce checkout
 */
export async function synchronizeFeeUpdate(
    storeHash: string,
    feeRule: FeeRuleData
): Promise<FeeSynchronizationResult> {
    const result: FeeSynchronizationResult = {
        success: false,
        action: 'update',
        details: {
            feeRuleId: feeRule.id || 'unknown',
            feeRuleName: feeRule.name,
            checkoutFound: false,
            feeHistoryFound: false,
            bigCommerceOperationSuccess: false
        }
    };

    try {
        console.log(`Starting fee synchronization for updated rule "${feeRule.name}"`);

        // Find recent checkout with applied fee
        const checkoutWithFee = await findRecentCheckoutWithAppliedFee(storeHash, feeRule);
        
        if (!checkoutWithFee) {
            result.error = 'No recent checkout found with applied fee for this rule';
            return result;
        }

        result.details!.feeHistoryFound = true;
        result.checkoutId = checkoutWithFee.checkoutId;
        result.bigCommerceFeeId = checkoutWithFee.feeId;

        // Validate checkout is still accessible
        const isCheckoutValid = await validateCheckout(storeHash, checkoutWithFee.checkoutId);
        
        if (!isCheckoutValid) {
            result.error = 'Checkout is no longer accessible';
            return result;
        }

        result.details!.checkoutFound = true;

        // Get checkout details for percentage calculation
        const checkoutData = await getCheckoutDetails(storeHash, checkoutWithFee.checkoutId);

        // Get customer data for the checkout to check customer group filtering
        let customerData;
        try {
            const checkoutDbData = await db.getRecentCheckoutIds(storeHash, 1)
                .then(checkouts => checkouts.length > 0 ? checkouts[0] : null);
            if (checkoutDbData && checkoutDbData.customer) {
                customerData = checkoutDbData.customer;
                console.log(`Retrieved customer data for checkout ${checkoutWithFee.checkoutId}:`, customerData);
            } else {
                console.log(`No customer data found for checkout ${checkoutWithFee.checkoutId}`);
            }
        } catch (error) {
            console.warn(`Failed to retrieve customer data for checkout ${checkoutWithFee.checkoutId}:`, error);
        }

        // Check if customer group filtering applies
        const customerGroupId = customerData?.customer_group_id;
        const selectedCustomerGroups = feeRule.selectedCustomerGroups || [];
        
        console.log(`Customer group ID: ${customerGroupId}, Selected customer groups: [${selectedCustomerGroups.join(', ')}]`);
        
        // If customer groups are specified and customer's group is not in the selected groups,
        // remove the fee instead of updating it
        if (selectedCustomerGroups.length > 0 && customerGroupId) {
            if (!selectedCustomerGroups.includes(customerGroupId)) {
                console.log(`Removing fee "${feeRule.name}" from checkout ${checkoutWithFee.checkoutId} - customer group ${customerGroupId} not in selected groups [${selectedCustomerGroups.join(', ')}]`);
                
                // Delete the fee from BigCommerce
                await deleteCheckoutFee(
                    storeHash,
                    checkoutWithFee.checkoutId,
                    checkoutWithFee.feeId
                );
                
                result.details!.bigCommerceOperationSuccess = true;
                result.success = true;
                console.log(`Successfully removed fee "${feeRule.name}" due to customer group filtering`);
                return result;
            }
        }
        
        // If customer groups are specified but no customer data is available,
        // remove the fee as we can't verify the customer's group
        if (selectedCustomerGroups.length > 0 && !customerGroupId) {
            console.log(`Removing fee "${feeRule.name}" from checkout ${checkoutWithFee.checkoutId} - customer data not available but fee requires specific customer groups`);

            // Delete the fee from BigCommerce
            await deleteCheckoutFee(
                storeHash,
                checkoutWithFee.checkoutId,
                checkoutWithFee.feeId
            );

            result.details!.bigCommerceOperationSuccess = true;
            result.success = true;
            console.log(`Successfully removed fee "${feeRule.name}" due to missing customer data`);
            return result;
        }

        // If no customer group restrictions, fee continues to apply
        if (selectedCustomerGroups.length === 0) {
            console.log(`Customer group validation passed for fee "${feeRule.name}" - no customer group restrictions (apply to all customer groups)`);
        }

        // Check if channel filtering applies
        let channelData;
        try {
            const checkoutDbData = await db.getRecentCheckoutIds(storeHash, 1)
                .then(checkouts => checkouts.length > 0 ? checkouts[0] : null);
            if (checkoutDbData && checkoutDbData.channel) {
                channelData = checkoutDbData.channel;
                console.log(`Retrieved channel data for checkout ${checkoutWithFee.checkoutId}:`, channelData);
            } else {
                console.log(`No channel data found for checkout ${checkoutWithFee.checkoutId}`);
            }
        } catch (error) {
            console.warn(`Failed to retrieve channel data for checkout ${checkoutWithFee.checkoutId}:`, error);
        }

        const channelId = extractChannelId(channelData);
        const selectedChannels = feeRule.selectedChannels || [];
        
        console.log(`Channel ID: ${channelId}, Selected channels: [${selectedChannels.join(', ')}]`);
        
        // If channels are specified and active channel is not in the selected channels,
        // remove the fee instead of updating it
        if (selectedChannels.length > 0 && channelId) {
            if (!selectedChannels.includes(channelId)) {
                console.log(`Removing fee "${feeRule.name}" from checkout ${checkoutWithFee.checkoutId} - channel ${channelId} not in selected channels [${selectedChannels.join(', ')}]`);
                
                // Delete the fee from BigCommerce
                await deleteCheckoutFee(
                    storeHash,
                    checkoutWithFee.checkoutId,
                    checkoutWithFee.feeId
                );
                
                result.details!.bigCommerceOperationSuccess = true;
                result.success = true;
                console.log(`Successfully removed fee "${feeRule.name}" due to channel filtering`);
                return result;
            }
        }
        
        // If channels are specified but no channel data is available,
        // remove the fee as we can't verify the active channel
        if (selectedChannels.length > 0 && !channelId) {
            console.log(`Removing fee "${feeRule.name}" from checkout ${checkoutWithFee.checkoutId} - channel data not available but fee requires specific channels`);

            // Delete the fee from BigCommerce
            await deleteCheckoutFee(
                storeHash,
                checkoutWithFee.checkoutId,
                checkoutWithFee.feeId
            );

            result.details!.bigCommerceOperationSuccess = true;
            result.success = true;
            console.log(`Successfully removed fee "${feeRule.name}" due to missing channel data`);
            return result;
        }

        // If no channel restrictions, fee continues to apply
        if (selectedChannels.length === 0) {
            console.log(`Channel validation passed for fee "${feeRule.name}" - no channel restrictions (apply to all channels)`);
        }

        // Calculate the updated fee cost using filtered calculation (supports brand/category filters)
        const calculatedCost = await calculateFilteredFeeCost(storeHash, checkoutData, feeRule);

        console.log(`Calculated updated fee cost for "${feeRule.name}": ${calculatedCost}`);
        console.log(`Fee rule filters - selectedProducts: [${(feeRule.selectedProducts || []).join(', ')}], selectedBrands: [${(feeRule.selectedBrands || []).join(', ')}], selectedCategories: [${(feeRule.selectedCategories || []).join(', ')}]`);

        // If calculated cost is 0 or very small, remove the fee instead of updating it
        if (calculatedCost === 0 || calculatedCost < 0.01) {
            console.log(`Removing fee "${feeRule.name}" from checkout ${checkoutWithFee.checkoutId} - calculated cost is ${calculatedCost} (no products match updated filters)`);

            // Delete the fee from BigCommerce
            await deleteCheckoutFee(
                storeHash,
                checkoutWithFee.checkoutId,
                checkoutWithFee.feeId
            );

            result.details!.bigCommerceOperationSuccess = true;
            result.success = true;
            console.log(`Successfully removed fee "${feeRule.name}" due to filter changes resulting in zero cost`);
            return result;
        }

        // Convert fee rule to BigCommerce format
        const feeRequest = convertFeeRuleToRequest(feeRule, calculatedCost);

        console.log(`Updating BigCommerce fee ${checkoutWithFee.feeId} in checkout ${checkoutWithFee.checkoutId} with new cost: ${calculatedCost}`);

        // Try to update the fee in BigCommerce, but handle the case where the fee was previously deleted
        try {
            // Attempt to update the existing fee
            const updatedFee = await updateCheckoutFee(
                storeHash,
                checkoutWithFee.checkoutId,
                checkoutWithFee.feeId,
                feeRequest
            );
            
            result.details!.bigCommerceOperationSuccess = true;
            result.bigCommerceFeeId = updatedFee.id;

            // Update tracking in database
            if (feeRule.id) {
                await db.updateFeeRuleTracking(
                    storeHash,
                    feeRule.id,
                    updatedFee.id,
                    checkoutWithFee.checkoutId,
                    updatedFee.cost_inc_tax || updatedFee.cost_ex_tax || updatedFee.cost || calculatedCost
                );
            }
            
            result.success = true;
            console.log(`Successfully updated existing fee for rule "${feeRule.name}"`);
            
        } catch (updateError) {
            // Check if the error is because the fee no longer exists (was previously deleted)
            if (updateError.status === 422 && 
                (updateError.title?.includes('not available') || 
                 updateError.detail?.includes('not available'))) {
                
                console.log(`Fee ${checkoutWithFee.feeId} is no longer available (likely deleted). Creating a new fee instead.`);
                
                // Create a new fee instead of updating
                const newFee = await applyCheckoutFee(
                    storeHash,
                    checkoutWithFee.checkoutId,
                    feeRequest
                );
                
                result.details!.bigCommerceOperationSuccess = true;
                result.bigCommerceFeeId = newFee.id;
                
                // Update tracking in database with the new fee ID
                if (feeRule.id) {
                    await db.updateFeeRuleTracking(
                        storeHash,
                        feeRule.id,
                        newFee.id,
                        checkoutWithFee.checkoutId,
                        newFee.cost_inc_tax || newFee.cost_ex_tax || newFee.cost || calculatedCost
                    );
                }
                
                result.success = true;
                console.log(`Successfully created new fee for rule "${feeRule.name}" after previous fee was deleted`);
                
            } else {
                // Re-throw if it's not the specific error we're handling
                throw updateError;
            }
        }

    } catch (error) {
        // Handle 404 and 422 errors gracefully - fee or checkout no longer exists
        if (error.status === 404 || (error.status === 422 && error.title?.includes('not available'))) {
            console.log(`Fee or checkout no longer exists (${error.status}) for rule "${feeRule.name}" - considering sync successful`);
            result.success = true;
            result.error = `Fee or checkout no longer exists (${error.status}) - considered successful`;
        } else {
            console.error(`Error synchronizing fee update for rule "${feeRule.name}":`, error);
            result.error = error.message || 'Unknown synchronization error';
        }
    }

    return result;
}

/**
 * Synchronize fee rule deletion with BigCommerce checkout
 */
export async function synchronizeFeeDeletion(
    storeHash: string,
    feeRule: FeeRuleData
): Promise<FeeSynchronizationResult> {
    const result: FeeSynchronizationResult = {
        success: false,
        action: 'delete',
        details: {
            feeRuleId: feeRule.id || 'unknown',
            feeRuleName: feeRule.name,
            checkoutFound: false,
            feeHistoryFound: false,
            bigCommerceOperationSuccess: false
        }
    };

    try {
        console.log(`Starting fee synchronization for deleted rule "${feeRule.name}"`);

        // Find recent checkout with applied fee
        const checkoutWithFee = await findRecentCheckoutWithAppliedFee(storeHash, feeRule);
        
        if (!checkoutWithFee) {
            result.error = 'No recent checkout found with applied fee for this rule';
            return result;
        }

        result.details!.feeHistoryFound = true;
        result.checkoutId = checkoutWithFee.checkoutId;
        result.bigCommerceFeeId = checkoutWithFee.feeId;

        // Validate checkout is still accessible
        const isCheckoutValid = await validateCheckout(storeHash, checkoutWithFee.checkoutId);
        
        if (!isCheckoutValid) {
            result.error = 'Checkout is no longer accessible';
            return result;
        }

        result.details!.checkoutFound = true;

        console.log(`Deleting BigCommerce fee ${checkoutWithFee.feeId} from checkout ${checkoutWithFee.checkoutId}`);

        // Delete the fee from BigCommerce using the enhanced deletion method
        await deleteCheckoutFee(
            storeHash,
            checkoutWithFee.checkoutId,
            checkoutWithFee.feeId
        );

        console.log(`Fee deletion completed for rule "${feeRule.name}"`);

        // Verify the fee was actually deleted by checking the checkout
        try {
            const updatedCheckoutData = await getCheckoutDetails(storeHash, checkoutWithFee.checkoutId);
            const remainingFees = updatedCheckoutData.fees || [];
            const deletedFeeStillExists = remainingFees.find(fee => fee.id === checkoutWithFee.feeId);

            if (deletedFeeStillExists) {
                console.warn(`Fee ${checkoutWithFee.feeId} still exists in checkout after deletion attempt`);
            } else {
                console.log(`Verified: Fee ${checkoutWithFee.feeId} successfully removed from checkout`);
            }
        } catch (verificationError) {
            // Don't fail the operation if verification fails
            console.warn(`Could not verify fee deletion:`, verificationError);
        }

        result.details!.bigCommerceOperationSuccess = true;
        result.success = true;
        console.log(`Successfully synchronized fee deletion for rule "${feeRule.name}"`);

    } catch (error) {
        // Handle 404 errors gracefully - fee or checkout no longer exists
        if (error.status === 404) {
            console.log(`Fee or checkout no longer exists (404) for rule "${feeRule.name}" - considering deletion successful`);
            result.success = true;
            result.error = 'Fee or checkout no longer exists (404) - considered successful';
        } else {
            console.error(`Error synchronizing fee deletion for rule "${feeRule.name}":`, error);
            result.error = error.message || 'Unknown synchronization error';
        }
    }

    return result;
}

/**
 * Synchronize fee rule activation with BigCommerce checkout
 * Applies the newly activated fee to the most recent checkout
 */
export async function synchronizeFeeActivation(
    storeHash: string,
    feeRule: FeeRuleData
): Promise<FeeSynchronizationResult> {
    const result: FeeSynchronizationResult = {
        success: false,
        action: 'update',
        details: {
            feeRuleId: feeRule.id || 'unknown',
            feeRuleName: feeRule.name,
            checkoutFound: false,
            feeHistoryFound: false,
            bigCommerceOperationSuccess: false
        }
    };

    try {
        console.log(`Starting fee activation synchronization for rule "${feeRule.name}"`);

        // Get the most recent checkout ID for this store
        const recentCheckouts = await db.getRecentCheckoutIds(storeHash, 1);

        if (recentCheckouts.length === 0) {
            result.error = 'No recent checkout found to apply the activated fee';
            return result;
        }

        const mostRecentCheckout = recentCheckouts[0];
        result.checkoutId = mostRecentCheckout.checkoutId;

        // Validate checkout is still accessible
        const isCheckoutValid = await validateCheckout(storeHash, mostRecentCheckout.checkoutId);

        if (!isCheckoutValid) {
            result.error = 'Most recent checkout is no longer accessible';
            return result;
        }

        result.details!.checkoutFound = true;

        // Get customer data for the checkout to check customer group filtering
        let customerData;
        try {
            const checkoutDbData = await db.getRecentCheckoutIds(storeHash, 1)
                .then(checkouts => checkouts.length > 0 ? checkouts[0] : null);
            if (checkoutDbData && checkoutDbData.customer) {
                customerData = checkoutDbData.customer;
                console.log(`Retrieved customer data for checkout ${mostRecentCheckout.checkoutId}:`, customerData);
            } else {
                console.log(`No customer data found for checkout ${mostRecentCheckout.checkoutId}`);
            }
        } catch (error) {
            console.warn(`Failed to retrieve customer data for checkout ${mostRecentCheckout.checkoutId}:`, error);
        }

        // Check if customer group filtering applies
        const customerGroupId = customerData?.customer_group_id;
        const selectedCustomerGroups = feeRule.selectedCustomerGroups || [];
        
        console.log(`Customer group ID: ${customerGroupId}, Selected customer groups: [${selectedCustomerGroups.join(', ')}]`);
        
        // If customer groups are specified and customer's group is not in the selected groups,
        // or if customer groups are specified but no customer data is available,
        // skip applying the fee
        if (selectedCustomerGroups.length > 0) {
            if (!customerGroupId) {
                console.log(`Skipping fee activation for "${feeRule.name}" - customer data not available but fee requires specific customer groups`);
                result.success = true; // Consider it successful since we're intentionally skipping
                result.error = 'Fee activation skipped - customer data not available but fee requires specific customer groups';
                return result;
            }
            
            if (!selectedCustomerGroups.includes(customerGroupId)) {
                console.log(`Skipping fee activation for "${feeRule.name}" - customer group ${customerGroupId} not in selected groups [${selectedCustomerGroups.join(', ')}]`);
                result.success = true; // Consider it successful since we're intentionally skipping
                result.error = `Fee activation skipped - customer group ${customerGroupId} not in selected groups`;
                return result;
            }
            
            console.log(`Customer group ${customerGroupId} matches fee rule "${feeRule.name}" selected groups [${selectedCustomerGroups.join(', ')}]`);
        } else {
            console.log(`Customer group validation passed for fee "${feeRule.name}" - no customer group restrictions (apply to all customer groups)`);
        }

        // Check if channel filtering applies
        let channelData;
        try {
            const checkoutDbData = await db.getRecentCheckoutIds(storeHash, 1)
                .then(checkouts => checkouts.length > 0 ? checkouts[0] : null);
            if (checkoutDbData && checkoutDbData.channel) {
                channelData = checkoutDbData.channel;
                console.log(`Retrieved channel data for checkout ${mostRecentCheckout.checkoutId}:`, channelData);
            } else {
                console.log(`No channel data found for checkout ${mostRecentCheckout.checkoutId}`);
            }
        } catch (error) {
            console.warn(`Failed to retrieve channel data for checkout ${mostRecentCheckout.checkoutId}:`, error);
        }

        // Check if channel filtering applies
        const channelId = extractChannelId(channelData);
        const selectedChannels = feeRule.selectedChannels || [];
        
        console.log(`Channel ID: ${channelId}, Selected channels: [${selectedChannels.join(', ')}]`);
        
        // If channels are specified and active channel is not in the selected channels,
        // or if channels are specified but no channel data is available,
        // skip applying the fee
        if (selectedChannels.length > 0) {
            if (!channelId) {
                console.log(`Skipping fee activation for "${feeRule.name}" - channel data not available but fee requires specific channels`);
                result.success = true; // Consider it successful since we're intentionally skipping
                result.error = 'Fee activation skipped - channel data not available but fee requires specific channels';
                return result;
            }
            
            if (!selectedChannels.includes(channelId)) {
                console.log(`Skipping fee activation for "${feeRule.name}" - channel ${channelId} not in selected channels [${selectedChannels.join(', ')}]`);
                result.success = true; // Consider it successful since we're intentionally skipping
                result.error = `Fee activation skipped - channel ${channelId} not in selected channels`;
                return result;
            }
            
            console.log(`Channel ${channelId} matches fee rule "${feeRule.name}" selected channels [${selectedChannels.join(', ')}]`);
        } else {
            console.log(`Channel validation passed for fee "${feeRule.name}" - no channel restrictions (apply to all channels)`);
        }

        console.log(`Applying activated fee "${feeRule.name}" to most recent checkout ${mostRecentCheckout.checkoutId}`);

        // Apply the specific fee rule to the most recent checkout using targeted approach
        const feeApplicationResult = await applyFeeRuleToMostRecentCheckout(storeHash, feeRule);

        if (feeApplicationResult.success) {
            if (feeApplicationResult.appliedFees.length > 0) {
                const appliedFee = feeApplicationResult.appliedFees[0];
                result.bigCommerceFeeId = appliedFee.id;
                result.details!.bigCommerceOperationSuccess = true;
                result.success = true;
                console.log(`Successfully applied activated fee "${feeRule.name}" with BigCommerce ID: ${appliedFee.id}`);
            } else {
                // Fee was not applied (likely due to filters not matching or fee already exists)
                console.log(`Fee "${feeRule.name}" was not applied - likely due to filters not matching or fee already exists`);
                result.success = true;
                result.error = 'Fee was not applied - likely due to filters not matching or fee already exists';
            }
        } else {
            const errorMessages = feeApplicationResult.errors.map(err => err.error).join('; ');
            result.error = `Fee application failed: ${errorMessages}`;
        }

    } catch (error) {
        // Handle 404 errors gracefully - checkout no longer exists
        if (error.status === 404) {
            console.log(`Checkout no longer exists (404) for fee activation "${feeRule.name}" - considering sync successful`);
            result.success = true;
            result.error = 'Checkout no longer exists (404) - considered successful';
        } else {
            console.error(`Error synchronizing fee activation for rule "${feeRule.name}":`, error);
            result.error = error.message || 'Unknown synchronization error';
        }
    }

    return result;
}
